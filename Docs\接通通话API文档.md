# 接通通话API文档

## 接口概述

接通通话API用于接听已发起的通话请求，将通话状态从"发起"变更为"接通"，并修改双方用户状态为忙碌。

## 接口信息

- **接口地址**: `/video/answer_call`
- **请求方式**: `POST`
- **接口说明**: 接通通话

## 请求参数

| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| call_id | int | 是 | 通话记录ID |

## 请求示例

```json
{
    "call_id": 123
}
```

## 响应参数

### 成功响应

```json
{
    "code": 1,
    "msg": "接通成功",
    "data": {
        "call_id": 123,
        "channel_id": "1234567890",
        "turn_time": 1672531200,
        "unit_price": 10,
        "caller_info": {
            "id": 1,
            "nickname": "发起人昵称",
            "avatar": "头像URL"
        },
        "callee_info": {
            "id": 2,
            "nickname": "接听人昵称", 
            "avatar": "头像URL"
        }
    }
}
```

### 失败响应

```json
{
    "code": 0,
    "msg": "错误信息",
    "data": []
}
```

## 业务逻辑

1. **权限验证**: 只有被叫用户才能接通通话
2. **状态检查**: 只能接通状态为"发起"(0)的通话
3. **用户状态检查**: 确保双方都不在其他通话中
4. **更新通话记录**: 
   - 状态改为"接通"(1)
   - 记录接通时间(turn_time)
5. **更新用户状态**: 双方用户is_busy字段设为1(忙碌)

## 错误码说明

| 错误信息 | 说明 |
|----------|------|
| 通话记录不存在 | call_id对应的通话记录不存在 |
| 该通话无法接通 | 通话状态不是"发起"状态 |
| 您没有权限接通此通话 | 当前用户不是被叫用户 |
| 发起人正在其他通话中 | 发起人已在其他通话中 |
| 您正在其他通话中 | 接听人已在其他通话中 |
| 更新通话记录失败 | 数据库更新失败 |

## 注意事项

1. 接通通话后，双方用户状态会变为忙碌(is_busy=1)
2. 通话记录的状态会从0(发起)变为1(接通)
3. 会记录接通时间(turn_time)用于后续计费
4. 接通后需要调用其他API进行实际的音视频通话
5. 通话结束时需要调用挂断API恢复用户状态

## 相关接口

- 发起通话: `/video/start_call`
- 挂断通话: `/video/hangup_call`
