<?php

namespace app\applent\logic\video;

use app\common\logic\BaseLogic;
use app\common\model\user\User;
use app\common\model\VideoCallRecord;
use app\common\service\agora\AgoraService;
use app\common\service\LogService;
use app\common\service\ConfigService;

use think\facade\Cache;
use think\facade\Db;
use app\common\model\user\UserBalance;
/**
 * 视频通话业务逻辑类
 */
class VideoCallLogic extends BaseLogic
{
    /**
     * 发起通话
     * @param array $params 通话参数
     * @return array|false
     */
    public static function startCall($params)
    {
        Db::startTrans();
        try {
            if($params['to_user_id'] == $params['user_id']){
                throw new \Exception('不能给自己发起通话');
            }

            // 检查用户状态
            if (VideoCallRecord::isUserInCall($params['user_id'])) {
                throw new \Exception('您正在通话中，无法发起新的通话');
            }

            if (VideoCallRecord::isUserInCall($params['to_user_id'])) {
                throw new \Exception('对方正在通话中，请稍后再试');
            }

            //查找接听人信息
            $toUserInfo = User::where('id', $params['to_user_id'])
                        ->field('id,nickname,avatar,sex,video_price,voice_price,is_service_staff,is_auth,is_disable')
                        ->find();
            if (!$toUserInfo) {
                throw new \Exception('您所拨打的用户不存在');
            }
            if($toUserInfo['is_disable']){
                throw new \Exception('该用户已禁用');
            }
            if(!$toUserInfo['is_service_staff'] && $toUserInfo['sex'] == 2 && $toUserInfo['is_auth'] != 1){
                throw new \Exception('对方未认证，无法发起通话');
            }
            //检查接听人每分钟价格
            $price = $params['call_type'] == 1 ? $toUserInfo['video_price'] : $toUserInfo['voice_price'];
            if($toUserInfo['is_service_staff']){
                $price = 0;
            }

            //查找发起人余额
            $userBalance = UserBalance::getUserBalance($params['user_id']);
            $beforeSenderBalance = $userBalance ? $userBalance['balance'] : 0;
            if ($price > $beforeSenderBalance) {
                throw new \Exception('余额不足，请先充值');
            }
            
            //生成频道ID和Token
            $agoraService = new AgoraService();
            $channelId = $agoraService->generateChannelId($params['user_id'], $params['to_user_id']);      //通道ID
            $callerToken = $agoraService->generateRtcToken($params['user_id'], $channelId, 'publisher');   //发起人Token
            $calleeToken = $agoraService->generateRtcToken($params['to_user_id'], $channelId, 'publisher');//接听人Token

            // 创建通话记录
            $callRecord = [
                'user_id'           => $params['user_id'],
                'call_be_user_id'   => $params['to_user_id'],
                'channel_id'        => $channelId,
                'status'            => 0,
                'type'              => $params['call_type'],
                'duration'          => 0,
                'unit_price'        => $price,
                'total_coin'        => 0,
                'hangup_user_id'    => 0,
                'ip'                => request()->ip(),
                'create_time'       =>time(),
                'end_time'          => 0,
                'turn_time'         => 0,
                'heartbeat_time'    => 0,
            ];
            $callRecordId = VideoCallRecord::insertGetId($callRecord);
            if (!$callRecordId) {
                return self::setError('创建通话记录失败');
            }

            Db::commit();

            return [
                'call_id'           => $callRecordId,
                'channel_id'        => $channelId,
                'user_token'        => $callerToken,
                'to_user_token'     => $calleeToken,
                'video_deduction'   => $price,
                'to_user_info'      => [
                    'id'            => $toUserInfo->id,
                    'nickname'      => $toUserInfo->nickname,
                    'avatar'        => $toUserInfo->avatar,
                    'video_price'   => $toUserInfo->video_price,
                    'voice_price'   => $toUserInfo->voice_price,
                ],
            ];

        } catch (\Exception $e) {
            Db::rollback();
            self::setError($e->getMessage());
            return false;
        }
    }

    /**
     * 接通通话
     * @param array $params 接通参数
     * @return array|false
     */
    public static function answerCall($params)
    {
        Db::startTrans();
        try {
            // 查找通话记录
            $callRecord = VideoCallRecord::where('id', $params['call_id'])->find();
            if (!$callRecord) {
                throw new \Exception('通话记录不存在');
            }

            // 检查通话状态（只能接通发起状态的通话）
            if ($callRecord['status'] != VideoCallRecord::STATUS_INITIATED) {
                throw new \Exception('该通话无法接通');
            }

            // 检查用户权限（只有被叫用户才能接通）
            if ($params['user_id'] != $callRecord['call_be_user_id']) {
                throw new \Exception('您没有权限接通此通话');
            }

            $currentTime = time();

            // 更新通话记录状态为已接通
            $updateData = [
                'status'        => VideoCallRecord::STATUS_CONNECTED,
                'turn_time'     => $currentTime,
                'update_time'   => $currentTime
            ];

            $result = VideoCallRecord::where('id', $params['call_id'])->update($updateData);
            if (!$result) {
                throw new \Exception('更新通话记录失败');
            }

            // 修改双方用户状态为忙碌
            User::where('id', $callRecord['user_id'])->update(['is_busy' => 1]);
            User::where('id', $callRecord['call_be_user_id'])->update(['is_busy' => 1]);

            Db::commit();

            return true;

        } catch (\Exception $e) {
            Db::rollback();
            self::setError($e->getMessage());
            return false;
        }
    }

    /**
     * 实时扣费API（前端每5秒轮询）
     * @param array $params 扣费参数
     * @return array|false
     */
    public static function realTimeCharge($params)
    {
        try {
            $where = [
                'id' => $params['call_id'],
                'status' => 1,
            ];
            // 查找通话记录
            $callRecord = VideoCallRecord::where($where)->find();
            if (!$callRecord) {
                throw new \Exception('通话记录不存在');
            }

            // 检查用户权限（只有付费方才能调用扣费接口）
            $payUserId = $callRecord['user_id']; // 发起人付费
            if ($params['user_id'] != $payUserId) {
                throw new \Exception('您没有权限调用此接口');
            }

            // 检查接通时间
            if (!$callRecord['turn_time']) {
                throw new \Exception('通话接通时间异常');
            }

            $currentTime = time();
            // 通话时长（秒）
            $callDuration = $currentTime - $callRecord['turn_time']; 
            //上次心跳时间
            $lastChargeTime = $callRecord['heartbeat_time'];    
             //每分钟扣费金币数量
            $unitPrice = $callRecord['unit_price'];        

            //查看当前余额
            $userBalance = UserBalance::getUserBalance($payUserId);
            $currentBalance = $userBalance ? $userBalance['balance'] : 0;

            // 如果没有心跳时间，检查是否满足首次扣费条件（通话满5秒）
            if (!$lastChargeTime) {
                if ($callDuration >= 5) {
                    // 首次扣费：检查余额是否足够当前+下次扣费
                    $requiredBalance = $unitPrice * 2; // 当前扣费 + 下一分钟扣费

                    if ($currentBalance < $requiredBalance) {
                        // 余额不足，结束通话
                        self::endCallForInsufficientBalance($callRecord);
                        throw new \Exception('余额不足以支付后续通话费用，通话已结束');
                    }

                    // 开始首次扣费
                    Db::startTrans();

                    // 扣除用户余额
                    $result = UserBalance::subBalance($payUserId, $unitPrice);
                    if (!$result) {
                        Db::rollback();
                        throw new \Exception('扣费失败');
                    }

                    // 更新通话记录的心跳时间和总消费
                    VideoCallRecord::where('id', $params['call_id'])
                        ->inc('total_coin', $unitPrice)
                        ->update([
                            'heartbeat_time' => $currentTime,
                            'update_time' => $currentTime
                        ]);

                    Db::commit();

                    $remainingBalance = $currentBalance - $unitPrice;

                    return [
                        'need_charge' => true,
                        'message' => '首次扣费成功',
                        'charged_amount' => $unitPrice,
                        'charged_minutes' => 1,
                        'call_duration' => $callDuration,
                        'remaining_balance' => $remainingBalance,
                        'last_charge_time' => $currentTime,
                        'next_charge_in' => 60,
                        'next_minute_cost' => $unitPrice,
                        'can_afford_next' => $remainingBalance >= $unitPrice,
                        'balance_warning' => $remainingBalance < ($unitPrice * 2)
                    ];
                } else {
                    // 通话时长不足5秒
                    return [
                        'need_charge' => false,
                        'message' => '通话时长不足5秒，无需扣费',
                        'call_duration' => $callDuration,
                        'next_charge_in' => 5 - $callDuration,
                        'current_balance' => $currentBalance,
                        'next_minute_cost' => $unitPrice,
                        'can_afford_next' => $currentBalance >= ($unitPrice * 2),
                        'balance_warning' => $currentBalance < ($unitPrice * 2)
                    ];
                }
            } else {
                // 检查距离上次扣费是否满60秒
                $timeSinceLastCharge = $currentTime - $lastChargeTime;
                if ($timeSinceLastCharge < 60) {
                    // 检查余额是否足够下次扣费
                    $userBalance = UserBalance::getUserBalance($payUserId);
                    $currentBalance = $userBalance ? $userBalance['balance'] : 0;

                    return [
                        'need_charge' => false,
                        'message' => '距离上次扣费不足1分钟，无需扣费',
                        'call_duration' => $callDuration,
                        'time_since_last_charge' => $timeSinceLastCharge,
                        'next_charge_in' => 60 - $timeSinceLastCharge,
                        'current_balance' => $currentBalance,
                        'next_minute_cost' => $unitPrice,
                        'can_afford_next' => $currentBalance >= $unitPrice,
                        'balance_warning' => $currentBalance < ($unitPrice * 2)
                    ];
                }
                // 满足扣费条件：距离上次扣费满60秒，继续执行扣费逻辑
            }

            // 后续扣费：距离上次扣费满60秒，执行扣费逻辑
            // 计算扣费金额（固定1分钟）
            $totalChargeAmount = $unitPrice;

            // 计算所需余额：当前扣费 + 下一分钟扣费
            $requiredBalance = $totalChargeAmount + $unitPrice;

            if ($currentBalance < $requiredBalance) {
                // 余额不足以支付当前和下一分钟扣费，结束通话
                self::endCallForInsufficientBalance($callRecord);
                throw new \Exception('余额不足以支付后续通话费用，通话已结束');
            }

            // 开始扣费
            Db::startTrans();

            // 扣除用户余额
            $result = UserBalance::subBalance($payUserId, $totalChargeAmount);
            if (!$result) {
                Db::rollback();
                throw new \Exception('扣费失败');
            }

            // 更新通话记录的心跳时间和总消费
            VideoCallRecord::where('id', $params['call_id'])
                ->inc('total_coin', $totalChargeAmount)
                ->update([
                    'heartbeat_time' => $currentTime,
                    'update_time' => $currentTime
                ]);

            Db::commit();

            $remainingBalance = $currentBalance - $totalChargeAmount;

            return [
                'need_charge' => true,
                'message' => '扣费成功',
                'charged_amount' => $totalChargeAmount,
                'charged_minutes' => 1,
                'call_duration' => $callDuration,
                'remaining_balance' => $remainingBalance,
                'last_charge_time' => $currentTime,
                'next_charge_in' => 60, // 下次扣费固定60秒后
                'next_minute_cost' => $unitPrice,
                'can_afford_next' => $remainingBalance >= $unitPrice,
                'balance_warning' => $remainingBalance < ($unitPrice * 2) // 余额不足两分钟时警告
            ];

        } catch (\Exception $e) {
            Db::rollback();
            self::setError($e->getMessage());
            return false;
        }
    }

    /**
     * 余额不足时结束通话
     * @param array $callRecord 通话记录
     * @return void
     */
    private static function endCallForInsufficientBalance($callRecord)
    {
        $currentTime = time();

        // 更新通话记录状态为结束
        VideoCallRecord::where('id', $callRecord['id'])->update([
            'status' => VideoCallRecord::STATUS_ENDED,
            'end_time' => $currentTime,
            'hangup_user_id' => $callRecord['user_id'], // 付费方挂断
            'update_time' => $currentTime
        ]);

        // 恢复双方用户状态
        User::where('id', $callRecord['user_id'])->update(['is_busy' => 0]);
        User::where('id', $callRecord['call_be_user_id'])->update(['is_busy' => 0]);
    }

    /**
     * 挂断通话（未接通时）
     * @param array $params 挂断参数
     * @return array|false
     */
    public static function hangupCall($params)
    {
        try {
            // 查找通话记录
            $callRecord = VideoCallRecord::where('id', $params['call_id'])->find();
            if (!$callRecord) {
                throw new \Exception('通话记录不存在');
            }

            // 只处理未接通的通话（状态为0）
            if ($callRecord['status']) {
                throw new \Exception('该通话已接通或已结束');
            }

            // 检查用户权限（只有通话参与者才能挂断）
            if (!in_array($params['user_id'], [$callRecord['user_id'], $callRecord['call_be_user_id']])) {
                throw new \Exception('您没有权限挂断此通话');
            }

            // 根据挂断类型设置状态
            $status = VideoCallRecord::STATUS_CANCELLED; // 默认取消

            switch ($params['hangup_type']) {
                case 1: // 超时
                    $status = VideoCallRecord::STATUS_TIMEOUT;
                    break;
                case 2: // 发起人挂断
                    if ($params['user_id'] != $callRecord['user_id']) {
                        throw new \Exception('只有发起人才能执行发起人挂断操作');
                    }
                    $status = VideoCallRecord::STATUS_CANCELLED;
                    break;
                case 3: // 接听人挂断（拒绝）
                    if ($params['user_id'] != $callRecord['call_be_user_id']) {
                        throw new \Exception('只有接听人才能执行接听人挂断操作');
                    }
                    $status = VideoCallRecord::STATUS_REJECTED;
                    break;
                case 4: // 系统挂断
                    $status = VideoCallRecord::STATUS_ABNORMAL;
                    break;
                default:
                    throw new \Exception('无效的挂断类型');
            }

            $currentTime = time();

            // 更新通话记录
            $updateData = [
                'status'          => $status,
                'end_time'        => $currentTime,
                'hangup_user_id'  => $params['user_id'],
                'update_time'     => $currentTime
            ];

            $result = VideoCallRecord::where('id', $params['call_id'])->update($updateData);
            if (!$result) {
                throw new \Exception('更新通话记录失败');
            }

            // 删除缓存
            Cache::delete('video_call_' . $callRecord['channel_id']);

            return true;

        } catch (\Exception $e) {
            self::setError($e->getMessage());
            return false;
        }
    }
}
