<?php

namespace app\applent\logic\video;

use app\common\logic\BaseLogic;
use app\common\model\user\User;
use app\common\model\VideoCallRecord;
use app\common\service\agora\AgoraService;
use app\common\service\LogService;
use app\common\service\ConfigService;

use think\facade\Cache;
use think\facade\Db;
use app\common\model\user\UserBalance;
/**
 * 视频通话业务逻辑类
 */
class VideoCallLogic extends BaseLogic
{
    /**
     * 发起通话
     * @param array $params 通话参数
     * @return array|false
     */
    public static function startCall($params)
    {
        Db::startTrans();
        try {
            if($params['to_user_id'] == $params['user_id']){
                throw new \Exception('不能给自己发起通话');
            }

            // 检查用户状态
            if (VideoCallRecord::isUserInCall($params['user_id'])) {
                throw new \Exception('您正在通话中，无法发起新的通话');
            }

            if (VideoCallRecord::isUserInCall($params['to_user_id'])) {
                throw new \Exception('对方正在通话中，请稍后再试');
            }

            //查找接听人信息
            $toUserInfo = User::where('id', $params['to_user_id'])
                        ->field('id,nickname,avatar,sex,video_price,voice_price,is_service_staff,is_auth,is_disable')
                        ->find();
            if (!$toUserInfo) {
                throw new \Exception('您所拨打的用户不存在');
            }
            if($toUserInfo['is_disable']){
                throw new \Exception('该用户已禁用');
            }
            if(!$toUserInfo['is_service_staff'] && $toUserInfo['sex'] == 2 && $toUserInfo['is_auth'] != 1){
                throw new \Exception('对方未认证，无法发起通话');
            }
            //检查接听人每分钟价格
            $price = $params['call_type'] == 1 ? $toUserInfo['video_price'] : $toUserInfo['voice_price'];
            if($toUserInfo['is_service_staff']){
                $price = 0;
            }

            //查找发起人余额
            $userBalance = UserBalance::getUserBalance($params['user_id']);
            $beforeSenderBalance = $userBalance ? $userBalance['balance'] : 0;
            if ($price > $beforeSenderBalance) {
                throw new \Exception('余额不足，请先充值');
            }
            
            //生成频道ID和Token
            $agoraService = new AgoraService();
            $channelId = $agoraService->generateChannelId($params['user_id'], $params['to_user_id']);      //通道ID
            $callerToken = $agoraService->generateRtcToken($params['user_id'], $channelId, 'publisher');   //发起人Token
            $calleeToken = $agoraService->generateRtcToken($params['to_user_id'], $channelId, 'publisher');//接听人Token

            // 创建通话记录
            $callRecord = [
                'user_id'           => $params['user_id'],
                'call_be_user_id'   => $params['to_user_id'],
                'channel_id'        => $channelId,
                'status'            => 0,
                'type'              => $params['call_type'],  
                'duration'          => 0,
                'unit_price'        => $price,
                'hangup_user_id'    => 0,
                'ip'                => request()->ip(),
                'create_time'       =>time(),
                'end_time'          => 0,
                'turn_time'         => 0,
                
            ];
            $callRecordId = VideoCallRecord::insertGetId($callRecord);
            if (!$callRecordId) {
                return self::setError('创建通话记录失败');
            }

            Db::commit();

            return [
                'call_id'           => $callRecordId,
                'channel_id'        => $channelId,
                'user_token'        => $callerToken,
                'to_user_token'     => $calleeToken,
                'video_deduction'   => $price,
                'to_user_info'      => [
                    'id'            => $toUserInfo->id,
                    'nickname'      => $toUserInfo->nickname,
                    'avatar'        => $toUserInfo->avatar,
                    'video_price'   => $toUserInfo->video_price,
                    'voice_price'   => $toUserInfo->voice_price,
                ],
            ];

        } catch (\Exception $e) {
            Db::rollback();
            self::setError($e->getMessage());
            return false;
        }
    }

    /**
     * 接通通话
     * @param array $params 接通参数
     * @return array|false
     */
    public static function answerCall($params)
    {
        Db::startTrans();
        try {
            // 查找通话记录
            $callRecord = VideoCallRecord::where('id', $params['call_id'])->find();
            if (!$callRecord) {
                throw new \Exception('通话记录不存在');
            }

            // 检查通话状态（只能接通发起状态的通话）
            if ($callRecord['status'] != VideoCallRecord::STATUS_INITIATED) {
                throw new \Exception('该通话无法接通');
            }

            // 检查用户权限（只有被叫用户才能接通）
            if ($params['user_id'] != $callRecord['call_be_user_id']) {
                throw new \Exception('您没有权限接通此通话');
            }

            $currentTime = time();

            // 更新通话记录状态为已接通
            $updateData = [
                'status'        => VideoCallRecord::STATUS_CONNECTED,
                'turn_time'     => $currentTime,
                'update_time'   => $currentTime
            ];

            $result = VideoCallRecord::where('id', $params['call_id'])->update($updateData);
            if (!$result) {
                throw new \Exception('更新通话记录失败');
            }

            // 修改双方用户状态为忙碌
            User::where('id', $callRecord['user_id'])->update(['is_busy' => 1]);
            User::where('id', $callRecord['call_be_user_id'])->update(['is_busy' => 1]);

            Db::commit();

            return true;

        } catch (\Exception $e) {
            Db::rollback();
            self::setError($e->getMessage());
            return false;
        }
    }

    /**
     * 挂断通话（未接通时）
     * @param array $params 挂断参数
     * @return array|false
     */
    public static function hangupCall($params)
    {
        try {
            // 查找通话记录
            $callRecord = VideoCallRecord::where('id', $params['call_id'])->find();
            if (!$callRecord) {
                throw new \Exception('通话记录不存在');
            }

            // 只处理未接通的通话（状态为0）
            if ($callRecord['status']) {
                throw new \Exception('该通话已接通或已结束');
            }

            // 检查用户权限（只有通话参与者才能挂断）
            if (!in_array($params['user_id'], [$callRecord['user_id'], $callRecord['call_be_user_id']])) {
                throw new \Exception('您没有权限挂断此通话');
            }

            // 根据挂断类型设置状态
            $status = VideoCallRecord::STATUS_CANCELLED; // 默认取消

            switch ($params['hangup_type']) {
                case 1: // 超时
                    $status = VideoCallRecord::STATUS_TIMEOUT;
                    break;
                case 2: // 发起人挂断
                    if ($params['user_id'] != $callRecord['user_id']) {
                        throw new \Exception('只有发起人才能执行发起人挂断操作');
                    }
                    $status = VideoCallRecord::STATUS_CANCELLED;
                    break;
                case 3: // 接听人挂断（拒绝）
                    if ($params['user_id'] != $callRecord['call_be_user_id']) {
                        throw new \Exception('只有接听人才能执行接听人挂断操作');
                    }
                    $status = VideoCallRecord::STATUS_REJECTED;
                    break;
                case 4: // 系统挂断
                    $status = VideoCallRecord::STATUS_ABNORMAL;
                    break;
                default:
                    throw new \Exception('无效的挂断类型');
            }

            $currentTime = time();

            // 更新通话记录
            $updateData = [
                'status'          => $status,
                'end_time'        => $currentTime,
                'hangup_user_id'  => $params['user_id'],
                'update_time'     => $currentTime
            ];

            $result = VideoCallRecord::where('id', $params['call_id'])->update($updateData);
            if (!$result) {
                throw new \Exception('更新通话记录失败');
            }

            // 删除缓存
            Cache::delete('video_call_' . $callRecord['channel_id']);

            return true;

        } catch (\Exception $e) {
            self::setError($e->getMessage());
            return false;
        }
    }
}
