<?php

namespace app\applent\logic\user;

use app\common\logic\BaseLogic;
use app\common\model\user\User;
use app\common\model\user\UserDynamic;
use app\common\model\user\UserDynamicLike;
use app\common\model\user\UserDynamicComment;
use app\common\model\userFollow\UserFollow;
use app\common\service\ConfigService;
use app\common\service\TimeService;
use think\facade\Db;
use think\facade\Cache;
use app\common\service\FileService;
/**
 * 用户动态逻辑层
 * Class DynamicLogic
 * @package app\applent\logic\user
 */
class DynamicLogic extends BaseLogic
{
    /**
     * @notes 发布动态
     * @param array $params
     * @return array|false
     */
    public static function publish(array $params)
    {
        try {
            // 检查发布频率限制
            $cacheKey = 'dynamic_publish_limit_' . $params['user_id'];
            $lastPublishTime = Cache::get($cacheKey);

            if ($lastPublishTime) {
                $remainingTime = 60 - (time() - $lastPublishTime);
                if ($remainingTime > 0) {
                    static::setError('请于' . $remainingTime . '秒之后发布');
                    return false;
                }
            }

            // 验证用户是否存在
            $user = User::where(['id' => $params['user_id']])->find();
            if (!$user) {
                static::setError('用户不存在');
                return false;
            }

            // 处理媒体文件（验证器已确保media_urls必填且为数组格式）
            $mediaUrls = $params['media_urls'];
            $url = [];
            if(!empty($mediaUrls)){
                foreach ($mediaUrls as &$value) {
                    $url[] = FileService::setFileUrl($value);
                }
            }

            // 检查动态是否需要审核
            $isDynamicExamine = ConfigService::get('systemconfig', 'is_dynamic_examine', 0);
            $dynamicStatus = 1; // 默认状态为正常

            if ($isDynamicExamine == 1) {
                // 需要审核，状态设为待审核
                $dynamicStatus = 0; // 0=待审核，1=正常，2=审核驳回
            }

            // 创建动态
            $dynamic = UserDynamic::create([
                'user_id'       => $params['user_id'],
                'content'       => $params['content'],
                'type'          => 1,
                'media_urls'    => $url,
                'status'        => $dynamicStatus,
                'privacy_type'  => $params['privacy_type'] ?? 0, // 默认为全部可见
                'like_count'    => 0,
                'comment_count' => 0,
                'create_time'   => time(),
                'update_time'   => time()
            ]);

            // 设置发布频率限制缓存（60秒）
            Cache::set($cacheKey, time(), 60);

            return [
                'isDynamicExamine' => $isDynamicExamine,
                'dynamic_id' => $dynamic->id,
            ];

        } catch (\Exception $e) {
            static::setError('发布失败：' . $e->getMessage());
            return false;
        }
    }

    /**
     * @notes 获取动态详情
     * @param array $params
     * @return array|false
     */
    public static function detail(array $params)
    {
        try {
            $dynamic = UserDynamic::with(['user' => function($query) {
                $query->field('id,nickname,avatar,sex,age,is_online,is_busy');
            }])
            ->where(['id' => $params['dynamic_id']])
            ->field('id,user_id,content,type,media_urls,like_count,comment_count,status,create_time')
            ->find();

            if(!$dynamic){
                return [];
            }

            // 检查当前用户是否点赞
            $isLiked = false;
            if ($params['current_user_id']) {
                $isLiked = UserDynamicLike::where([
                    'dynamic_id' => $dynamic->id,
                    'user_id' => $params['current_user_id']
                ])->count() > 0;
            }

            // 检查当前用户是否关注了动态作者
            $followStatus = self::getFollowStatus($params['current_user_id'], $dynamic->user_id);

            return [
                'dynamic_id'    => $dynamic->id,
                'user_info'     => [
                    'user_id'   => $dynamic->user->id,
                    'nickname'  => $dynamic->user->nickname,
                    'avatar'    => $dynamic->user->avatar,
                    'sex'       => $dynamic->user->sex,
                    'age'       => $dynamic->user->age,
                    'is_online' => $dynamic->user->is_online,
                    'is_busy'   => $dynamic->user->is_busy,
                ],
                'follow_status' => $followStatus, 
                'content'       => $dynamic->content,
                'type'          => $dynamic->type,
                'media_urls'    => $dynamic->media_urls,
                'like_count'    => $dynamic->like_count,
                'comment_count' => $dynamic->comment_count,
                'status'        => $dynamic->status,
                'is_liked'      => $isLiked,
                'create_time'   => TimeService::getRelativeTime(strtotime($dynamic->create_time))
            ];

        } catch (\Exception $e) {
            static::setError('获取失败：' . $e->getMessage());
            return false;
        }
    }

    /**
     * @notes 点赞/取消点赞动态
     * @param array $params
     * @return array|false
     */
    public static function like(array $params)
    {
        try {
            // 验证动态是否存在
            $dynamic = UserDynamic::where(['id' => $params['dynamic_id']])->find();
            if (!$dynamic) {
                static::setError('动态不存在或已删除');
                return false;
            }
            if($dynamic->status != 1){
                static::setError('当前动态审核中或未通过');
                return false;
            }

            Db::startTrans();

            $existingLike = UserDynamicLike::where([
                'dynamic_id' => $params['dynamic_id'],
                'user_id' => $params['user_id']
            ])->find();

            if (!$existingLike) {
                // 点赞
                UserDynamicLike::create([
                    'dynamic_id' => $params['dynamic_id'],
                    'user_id' => $params['user_id'],
                    'create_time' => time()
                ]);
                // 更新动态点赞数
                UserDynamic::where('id', $params['dynamic_id'])->inc('like_count', 1)->update();
                $type = 1;

            } else {
                // 取消点赞
                $existingLike->delete();
                // 更新动态点赞数
                UserDynamic::where('id', $params['dynamic_id'])->dec('like_count', 1)->update();
                $type = 2;
            }

            Db::commit();

            return [
                'type' => $type
            ];

        } catch (\Exception $e) {
            Db::rollback();
            static::setError('操作失败：' . $e->getMessage());
            return false;
        }
    }

    /**
     * @notes 评论动态
     * @param array $params
     * @return array|false
     */
    public static function comment(array $params)
    {
        try {
            // 验证动态是否存在
            $dynamic = UserDynamic::where(['id' => $params['dynamic_id']])->find();
            if (!$dynamic) {
                static::setError('动态不存在或已删除');
                return false;
            }

            if($dynamic->status != 1){
                static::setError('当前动态审核中或未通过');
                return false;
            }

            Db::startTrans();

            // 检查评论审核配置
            $isCommentExamine = ConfigService::get('systemconfig', 'is_comment_examine', 0);

            // 根据配置设置评论状态
            $commentStatus = $isCommentExamine == 1 ? 0 : 1; // 0=待审核，1=审核通过

            // 创建评论
            $comment = UserDynamicComment::create([
                'dynamic_id'    => $params['dynamic_id'],
                'user_id'       => $params['user_id'],
                'content'       => $params['comment_content'],
                'parent_id'     => 0, // 一级评论
                'reply_to_user_id' => 0,
                'status'        => $commentStatus,
                'create_time'   => time(),
                'update_time'   => time()
            ]);

            // 只有审核通过的评论才更新动态评论数
            if ($commentStatus == 1) {
                UserDynamic::where('id', $params['dynamic_id'])->inc('comment_count', 1)->update();
            }

            Db::commit();

            return true;

        } catch (\Exception $e) {
            Db::rollback();
            static::setError('评论失败：' . $e->getMessage());
            return false;
        }
    }

    /**
     * @notes 删除动态
     * @param array $params
     * @return bool
     */
    public static function delete(array $params)
    {
        try {
            // 验证动态是否存在且属于当前用户
            $dynamic = UserDynamic::where([
                'id' => $params['dynamic_id'],
                'user_id' => $params['user_id']
            ])->find();

            if (!$dynamic) {
                static::setError('动态不存在或无权限删除');
                return false;
            }

            Db::startTrans();

            // 软删除动态
            $dynamic->delete();

            Db::commit();

            return true;

        } catch (\Exception $e) {
            Db::rollback();
            static::setError('删除失败：' . $e->getMessage());
            return false;
        }
    }

    /**
     * @notes 修改动态可见范围
     * @param array $params
     * @return bool
     */
    public static function updatePrivacy(array $params)
    {
        try {
            // 查找动态
            $dynamic = UserDynamic::where([
                'id' => $params['dynamic_id'],
                'user_id' => $params['user_id'] // 确保只能修改自己的动态
            ])->find();

            if (!$dynamic) {
                static::setError('动态不存在或无权限修改');
                return false;
            }

            // 更新隐私类型
            $result = $dynamic->save([
                'privacy_type' => $params['new_privacy_type'],
                'update_time' => time()
            ]);

            if (!$result) {
                static::setError('修改失败');
                return false;
            }

            return true;

        } catch (\Exception $e) {
            static::setError('修改失败：' . $e->getMessage());
            return false;
        }
    }

    /**
     * @notes 获取用户之间的关注状态
     * @param int $currentUserId 当前用户ID
     * @param int $targetUserId 目标用户ID
     * @return int 0=未关注，1=已关注
     */
    private static function getFollowStatus($currentUserId, $targetUserId)
    {
        // 默认状态：未关注
        $followStatus = 0;

        // 如果当前用户未登录或查看自己的动态，返回默认状态
        if (!$currentUserId || $currentUserId == $targetUserId) {
            return $followStatus;
        }

        // 查询我对目标用户的关注状态
        $myFollowRecord = UserFollow::where([
            'user_id' => $currentUserId,
            'to_user_id' => $targetUserId
        ])->find();

        // 设置我对他的关注状态
        if ($myFollowRecord && ($myFollowRecord['status'] == 1 || $myFollowRecord['status'] == 3)) {
            $followStatus = 1;
        }

        return $followStatus;
    }
}
