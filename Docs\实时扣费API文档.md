# 实时扣费API文档

## 接口概述

实时扣费API用于通话过程中的按分钟扣费，前端需要每5秒轮询此接口，系统会自动检查是否需要扣费并执行相应操作。

## 接口信息

- **接口地址**: `/video/real_time_charge`
- **请求方式**: `POST`
- **轮询频率**: 每5秒调用一次
- **接口说明**: 实时检查并执行通话扣费

## 请求参数

| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| call_id | int | 是 | 通话记录ID |

## 请求示例

```json
{
    "call_id": 123
}
```

## 响应参数

### 成功响应（无需扣费）

```json
{
    "code": 1,
    "msg": "扣费检查完成",
    "data": {
        "need_charge": false,
        "message": "通话时长不足1分钟，无需扣费",
        "call_duration": 45,
        "next_charge_in": 15,
        "current_balance": 100,
        "next_minute_cost": 10,
        "can_afford_next": true,
        "balance_warning": false
    }
}
```

### 成功响应（已扣费）

```json
{
    "code": 1,
    "msg": "扣费检查完成",
    "data": {
        "need_charge": true,
        "message": "扣费成功",
        "charged_amount": 10,
        "charged_minutes": 1,
        "call_duration": 65,
        "remaining_balance": 90,
        "next_charge_in": 55,
        "next_minute_cost": 10,
        "can_afford_next": true,
        "balance_warning": false
    }
}
```

### 成功响应（余额警告）

```json
{
    "code": 1,
    "msg": "扣费检查完成",
    "data": {
        "need_charge": false,
        "message": "当前分钟已扣费，等待下一分钟",
        "call_duration": 125,
        "charged_minutes": 2,
        "next_charge_in": 55,
        "current_balance": 15,
        "next_minute_cost": 10,
        "can_afford_next": true,
        "balance_warning": true
    }
}
```

### 失败响应

```json
{
    "code": 0,
    "msg": "余额不足，通话已结束",
    "data": []
}
```

## 业务逻辑

### 扣费规则

1. **时间检查**: 只有通话时长满60秒才开始扣费
2. **按分钟扣费**: 每满1分钟扣除1分钟的费用
3. **避免重复扣费**: 系统记录已扣费分钟数，避免重复扣费
4. **余额检查**: 扣费前检查余额是否足够支付当前费用和下一分钟费用
5. **余额预警**: 当余额不足两分钟通话费用时发出警告

### 扣费流程

1. **状态验证**: 检查通话是否处于接通状态
2. **权限验证**: 只有付费方（发起人）可以调用
3. **时长计算**: 计算从接通时间到当前的通话时长
4. **扣费计算**: 计算需要扣费的分钟数和金额
5. **余额检查**: 检查用户余额是否足够支付当前费用和下一分钟费用
6. **执行扣费**: 扣除用户余额并更新记录
7. **更新记录**: 更新通话记录和扣费记录
8. **余额预警**: 检查剩余余额是否足够后续通话

### 自动结束通话

当用户余额不足时，系统会自动：
1. 结束通话（状态改为已结束）
2. 恢复双方用户状态（is_busy=0）
3. 记录挂断用户为付费方

## 响应字段说明

| 字段名 | 类型 | 说明 |
|--------|------|------|
| need_charge | boolean | 是否执行了扣费 |
| message | string | 操作结果描述 |
| call_duration | int | 通话时长（秒） |
| charged_amount | int | 本次扣费金额 |
| charged_minutes | int | 本次扣费分钟数 |
| remaining_balance | int | 扣费后剩余余额 |
| current_balance | int | 当前余额（未扣费时显示） |
| next_charge_in | int | 距离下次扣费的秒数 |
| next_minute_cost | int | 下一分钟扣费金额 |
| can_afford_next | boolean | 是否能支付下一分钟费用 |
| balance_warning | boolean | 余额警告（不足两分钟费用时为true） |

## 错误码说明

| 错误信息 | 说明 |
|----------|------|
| 通话记录不存在 | call_id对应的通话记录不存在 |
| 通话未接通，无需扣费 | 通话状态不是接通状态 |
| 您没有权限调用此接口 | 当前用户不是付费方 |
| 通话接通时间异常 | turn_time字段为空 |
| 余额不足以支付后续通话费用，通话已结束 | 用户余额不足以支付当前和下一分钟费用，系统自动结束通话 |
| 扣费失败 | 数据库操作失败 |

## 前端轮询示例

```javascript
// 每5秒轮询扣费接口
function startChargePolling(callId) {
    const interval = setInterval(async () => {
        try {
            const response = await fetch('/video/real_time_charge', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'Authorization': 'Bearer ' + token
                },
                body: JSON.stringify({ call_id: callId })
            });
            
            const result = await response.json();
            
            if (result.code === 1) {
                const data = result.data;
                
                // 更新UI显示
                if (data.need_charge) {
                    console.log(`扣费成功: ${data.charged_amount}金币`);
                    updateBalance(data.remaining_balance);
                } else if (data.current_balance !== undefined) {
                    updateBalance(data.current_balance);
                }

                // 余额警告处理
                if (data.balance_warning) {
                    showBalanceWarning(`余额不足，仅够通话${Math.floor((data.current_balance || data.remaining_balance) / data.next_minute_cost)}分钟`);
                }

                // 检查是否能支付下一分钟
                if (!data.can_afford_next) {
                    console.warn('余额不足以支付下一分钟费用，通话可能即将结束');
                    showBalanceWarning('余额不足，通话即将结束');
                }

                // 显示下次扣费倒计时
                updateChargeCountdown(data.next_charge_in);
                
            } else {
                // 扣费失败，可能是余额不足或通话结束
                console.error('扣费失败:', result.msg);
                clearInterval(interval);
                endCall();
            }
            
        } catch (error) {
            console.error('轮询请求失败:', error);
        }
    }, 5000); // 每5秒执行一次
    
    return interval;
}
```

## 注意事项

1. **轮询频率**: 建议每5秒轮询一次，不要过于频繁
2. **错误处理**: 当接口返回失败时，应停止轮询并结束通话
3. **网络异常**: 处理网络请求失败的情况
4. **用户体验**: 在UI上显示扣费状态和余额变化
5. **通话结束**: 挂断通话时记得停止轮询

## 相关接口

- 发起通话: `/video/start_call`
- 接通通话: `/video/answer_call`  
- 挂断通话: `/video/hangup_call`
