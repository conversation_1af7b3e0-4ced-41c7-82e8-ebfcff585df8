# 实时扣费API测试示例

## 测试场景

### 场景1: 通话时长不足1分钟

**请求**:
```json
POST /video/real_time_charge
{
    "call_id": 123
}
```

**预期响应**:
```json
{
    "code": 1,
    "msg": "扣费检查完成",
    "data": {
        "need_charge": false,
        "message": "通话时长不足1分钟，无需扣费",
        "call_duration": 45,
        "next_charge_in": 15
    }
}
```

### 场景2: 首次扣费（通话1分钟）

**请求**:
```json
POST /video/real_time_charge
{
    "call_id": 123
}
```

**预期响应**:
```json
{
    "code": 1,
    "msg": "扣费检查完成",
    "data": {
        "need_charge": true,
        "message": "扣费成功",
        "charged_amount": 10,
        "charged_minutes": 1,
        "call_duration": 65,
        "remaining_balance": 90,
        "next_charge_in": 55
    }
}
```

### 场景3: 重复调用（同一分钟内）

**请求**:
```json
POST /video/real_time_charge
{
    "call_id": 123
}
```

**预期响应**:
```json
{
    "code": 1,
    "msg": "扣费检查完成",
    "data": {
        "need_charge": false,
        "message": "当前分钟已扣费，等待下一分钟",
        "call_duration": 70,
        "charged_minutes": 1,
        "next_charge_in": 50
    }
}
```

### 场景4: 余额不足

**请求**:
```json
POST /video/real_time_charge
{
    "call_id": 123
}
```

**预期响应**:
```json
{
    "code": 0,
    "msg": "余额不足，通话已结束",
    "data": []
}
```

## 数据库状态验证

### 扣费前数据状态

**la_video_call_record表**:
```sql
SELECT id, user_id, call_be_user_id, status, turn_time, total_coin, unit_price 
FROM la_video_call_record 
WHERE id = 123;
```

预期结果:
```
id: 123
user_id: 1
call_be_user_id: 2  
status: 1 (已接通)
turn_time: 1672531200
total_coin: 0
unit_price: 10
```

**la_user_balance表**:
```sql
SELECT user_id, balance FROM la_user_balance WHERE user_id = 1;
```

预期结果:
```
user_id: 1
balance: 100
```

### 扣费后数据状态

**la_video_call_record表**:
```sql
SELECT id, total_coin FROM la_video_call_record WHERE id = 123;
```

预期结果:
```
id: 123
total_coin: 10 (增加了10金币)
```

**la_user_balance表**:
```sql
SELECT user_id, balance FROM la_user_balance WHERE user_id = 1;
```

预期结果:
```
user_id: 1
balance: 90 (减少了10金币)
```

**video_charging_record表**:
```sql
SELECT user_id, to_user_id, channel_id, coin, sum, single_coin 
FROM video_charging_record 
WHERE channel_id = 'channel_123';
```

预期结果:
```
user_id: 1
to_user_id: 2
channel_id: channel_123
coin: 10
sum: 1
single_coin: 10
```

## 前端集成测试

### HTML测试页面

```html
<!DOCTYPE html>
<html>
<head>
    <title>实时扣费API测试</title>
</head>
<body>
    <div id="status">等待开始...</div>
    <div id="balance">余额: --</div>
    <div id="duration">通话时长: 0秒</div>
    <div id="charged">已扣费: 0金币</div>
    <div id="countdown">下次扣费: --</div>
    
    <button onclick="startTest()">开始测试</button>
    <button onclick="stopTest()">停止测试</button>

    <script>
        let pollingInterval;
        let startTime = Date.now();
        
        function startTest() {
            const callId = 123; // 测试用的通话ID
            
            pollingInterval = setInterval(async () => {
                try {
                    const response = await fetch('/video/real_time_charge', {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json',
                            'Authorization': 'Bearer your_token_here'
                        },
                        body: JSON.stringify({ call_id: callId })
                    });
                    
                    const result = await response.json();
                    
                    // 更新通话时长
                    const duration = Math.floor((Date.now() - startTime) / 1000);
                    document.getElementById('duration').textContent = `通话时长: ${duration}秒`;
                    
                    if (result.code === 1) {
                        const data = result.data;
                        
                        document.getElementById('status').textContent = data.message;
                        
                        if (data.need_charge) {
                            document.getElementById('balance').textContent = `余额: ${data.remaining_balance}`;
                            document.getElementById('charged').textContent = `已扣费: ${data.charged_amount}金币`;
                        }
                        
                        if (data.next_charge_in) {
                            document.getElementById('countdown').textContent = `下次扣费: ${data.next_charge_in}秒`;
                        }
                        
                    } else {
                        document.getElementById('status').textContent = `错误: ${result.msg}`;
                        stopTest();
                    }
                    
                } catch (error) {
                    document.getElementById('status').textContent = `请求失败: ${error.message}`;
                }
            }, 5000);
        }
        
        function stopTest() {
            if (pollingInterval) {
                clearInterval(pollingInterval);
                pollingInterval = null;
            }
            document.getElementById('status').textContent = '测试已停止';
        }
    </script>
</body>
</html>
```

## 性能测试

### 并发测试

使用工具（如Apache Bench）测试API的并发处理能力：

```bash
# 测试100个并发请求
ab -n 100 -c 10 -H "Content-Type: application/json" -p post_data.json http://your-domain/video/real_time_charge
```

post_data.json内容：
```json
{"call_id": 123}
```

### 预期性能指标

- **响应时间**: < 200ms
- **并发处理**: 支持至少50个并发请求
- **错误率**: < 1%

## 注意事项

1. **测试环境**: 确保测试环境有完整的数据库表结构
2. **测试数据**: 准备好测试用的用户、通话记录和余额数据
3. **权限验证**: 确保测试用户有正确的权限
4. **清理数据**: 测试完成后清理测试数据
5. **日志监控**: 观察系统日志确保没有异常
