<?php

namespace app\common\enum;

/**
 * IM消息类型枚举
 * Class ImMessageEnum
 * @package app\common\enum
 */
class ImMessageEnum
{
    // IM 消息类型常量
    const GIFT_MESSAGE = 1; // 礼物消息
    const GLOBAL_GIFT = 777; // 全局礼物
    const BROADCST_FLOATING_SCREEN = 778; // 全局广播飘屏
    const EXIT_LIVE_ROOM = 6; // 退出直播间消息
    const SHIMEI_NEWS = 61; // 正在连麦的下麦消息
    const AUDIENCE_LIST = 42; // 取前50条观众列表，IM推送到客户端
    const PK_INFORMATION = 31; // PK信息
    const PAY_ON_TIME_LIVE = 32; // 按时收费直播（全体推送的，用于通知用户即将进入收费直播）
    const LIVE_STREAMING_BY_VENUR = 40; // 按场收费直播（全体推送的，用于通知用户即将进入收费直播）
    const LIVE_BROADCAST_ENDS = 7; // 直播间结束消息
    const PK_ENDS = 24; // PK结束
    const CLOSE_VIDEO_CALL = 25; // 关闭视频通话
    const NOICE_VIOLATION = 27; // 违规通知
    const ONE_ONE_WARNING_PUSH = 114; // 一对一视频警告推送
    const ONE_ONE_HANG_UP = 14; // 一对一视频消息挂断推送
    const NOT_CONNECTED_DISCOMMECTED = 13; // 未接通挂断：一对一视频消息挂断推送
    const ADMIN_ONE_ONE_HANG_UP = 26; // 管理员：一对一视频消息挂断推送
    const ONE_ONE_REQUEST = 12; // 一对一视频消息请求推送

    // 新增视频通话消息类型
    const VIDEO_CALL_INVITE = 10; // 视频通话邀请
    const VOICE_CALL_INVITE = 11; // 语音通话邀请
    const CALL_ACCEPT = 12; // 通话接受
    const CALL_REJECT = 13; // 通话拒绝
    const CALL_HANGUP = 14; // 通话挂断
    const CALL_TIMEOUT = 16; // 通话超时
    const CALL_CANCEL = 17; // 通话取消

    const MATCH_MESSAGES = 28; // 匹配消息
    const AUDIENCE_ENTERS_ROOM = 5; // 观众进入房间
    const AUDIENCE_EXIT_ROOM = 6; // 观众退出房间
    const KICKING_PEOPLE = 41; // 踢人
    const SET_ADMINISTRATOR = 42; // 设置管理员
    const FORBIDDEN_SPEECH = 3; // 禁言
    const ACCOST_MESSAGE = 15; // 搭讪消息
    const BLOCK_DEL_IM_MESSAGE = 779; // 拉黑删除im聊天列表

    /**
     * @notes 获取消息类型描述
     * @param int|bool $type
     * @return array|string
     */
    public static function getTypeDesc($type = true)
    {
        $data = [
            self::GIFT_MESSAGE => '礼物消息',
            self::GLOBAL_GIFT => '全局礼物',
            self::BROADCST_FLOATING_SCREEN => '全局广播飘屏',
            self::EXIT_LIVE_ROOM => '退出直播间消息',
            self::SHIMEI_NEWS => '正在连麦的下麦消息',
            self::AUDIENCE_LIST => '观众列表推送',
            self::PK_INFORMATION => 'PK信息',
            self::PAY_ON_TIME_LIVE => '按时收费直播',
            self::LIVE_STREAMING_BY_VENUR => '按场收费直播',
            self::LIVE_BROADCAST_ENDS => '直播间结束消息',
            self::PK_ENDS => 'PK结束',
            self::CLOSE_VIDEO_CALL => '关闭视频通话',
            self::NOICE_VIOLATION => '违规通知',
            self::ONE_ONE_WARNING_PUSH => '一对一视频警告推送',
            self::ONE_ONE_HANG_UP => '一对一视频消息挂断推送',
            self::NOT_CONNECTED_DISCOMMECTED => '未接通挂断推送',
            self::ADMIN_ONE_ONE_HANG_UP => '管理员挂断推送',
            self::ONE_ONE_REQUEST => '一对一视频消息请求推送',

            // 新增视频通话消息类型描述
            self::VIDEO_CALL_INVITE => '视频通话邀请',
            self::VOICE_CALL_INVITE => '语音通话邀请',
            self::CALL_ACCEPT => '通话接受',
            self::CALL_REJECT => '通话拒绝',
            self::CALL_HANGUP => '通话挂断',
            self::CALL_TIMEOUT => '通话超时',
            self::CALL_CANCEL => '通话取消',

            self::MATCH_MESSAGES => '匹配消息',
            self::AUDIENCE_ENTERS_ROOM => '观众进入房间',
            self::AUDIENCE_EXIT_ROOM => '观众退出房间',
            self::KICKING_PEOPLE => '踢人',
            self::SET_ADMINISTRATOR => '设置管理员',
            self::FORBIDDEN_SPEECH => '禁言',
            self::ACCOST_MESSAGE => '搭讪消息',
            self::BLOCK_DEL_IM_MESSAGE => '拉黑删除聊天列表',
        ];

        if ($type === true) {
            return $data;
        }

        return $data[$type] ?? '未知消息类型';
    }

    /**
     * @notes 获取视频通话相关消息类型
     * @return array
     */
    public static function getVideoCallTypes()
    {
        return [
            self::ONE_ONE_WARNING_PUSH,
            self::ONE_ONE_HANG_UP,
            self::NOT_CONNECTED_DISCOMMECTED,
            self::ADMIN_ONE_ONE_HANG_UP,
            self::ONE_ONE_REQUEST,
            self::CLOSE_VIDEO_CALL,
            // 新增的视频通话消息类型
            self::VIDEO_CALL_INVITE,
            self::VOICE_CALL_INVITE,
            self::CALL_ACCEPT,
            self::CALL_REJECT,
            self::CALL_HANGUP,
            self::CALL_TIMEOUT,
            self::CALL_CANCEL,
        ];
    }

    /**
     * @notes 获取直播相关消息类型
     * @return array
     */
    public static function getLiveTypes()
    {
        return [
            self::EXIT_LIVE_ROOM,
            self::SHIMEI_NEWS,
            self::AUDIENCE_LIST,
            self::PK_INFORMATION,
            self::PAY_ON_TIME_LIVE,
            self::LIVE_STREAMING_BY_VENUR,
            self::LIVE_BROADCAST_ENDS,
            self::PK_ENDS,
            self::AUDIENCE_ENTERS_ROOM,
            self::AUDIENCE_EXIT_ROOM,
        ];
    }

    /**
     * @notes 获取管理相关消息类型
     * @return array
     */
    public static function getAdminTypes()
    {
        return [
            self::KICKING_PEOPLE,
            self::SET_ADMINISTRATOR,
            self::FORBIDDEN_SPEECH,
            self::NOICE_VIOLATION,
            self::ADMIN_ONE_ONE_HANG_UP,
        ];
    }
}
