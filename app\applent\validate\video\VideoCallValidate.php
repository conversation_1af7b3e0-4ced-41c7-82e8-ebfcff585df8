<?php

namespace app\applent\validate\video;

use app\common\validate\BaseValidate;
use app\common\model\user\User;
use app\common\model\VideoCallRecord;
/**
 * 视频通话验证器
 */
class VideoCallValidate extends BaseValidate
{
    protected $rule = [
        // 发起通话
        'to_user_id'    => 'require',
        'call_type'     => 'require|in:1,2',

        // 接通通话
        'call_id'       => 'require',

        // 挂断通话
        'hangup_type'   => 'require|in:1,2,3,4',
    ];

    protected $message = [
        'to_user_id.require'    => '请选择通话对象',

        'call_type.require'     => '请选择通话类型',
        'call_type.in'          => '通话类型只能是1(视频)或2(语音)',

        'call_id.require'       => '通话ID不能为空',

        'hangup_type.require'   => '挂断类型不能为空',
        'hangup_type.in'        => '挂断类型错误',
    ];

    protected $scene = [
        // 发起通话场景
        'start_call' => ['to_user_id', 'call_type'],
        // 接通通话场景
        'answer_call' => ['call_id'],
        // 挂断通话场景
        'hangup_call' => ['call_id', 'hangup_type'],
    ];
}
