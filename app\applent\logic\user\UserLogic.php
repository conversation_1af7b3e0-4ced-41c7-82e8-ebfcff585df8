<?php

namespace app\applent\logic\user;

use app\common\logic\BaseLogic;
use app\common\model\user\User;
use app\common\model\user\UserCareer;
use app\common\model\user\UserPhotoWall;
use app\common\model\user\UserDataReview;
use app\common\model\user\UserLabel;
use app\common\model\user\UserImageLabel;
use app\common\model\userFollow\UserFollow;
use app\common\model\user\UserVisitor;
use app\common\cache\UserFollowCache;
use app\common\service\FileService;
use app\common\service\ConfigService;
use think\facade\Db;
use think\facade\Cache;
use app\common\model\user\UserBalance;
/**
 * 用户逻辑
 * Class SmsLogic
 * @package app\api\logic
 */
class UserLogic extends BaseLogic
{
    /**
     * @notes 获取用户详细个人信息
     * @param int $userId 要查看的用户ID
     * @param int $currentUserId 当前登录用户ID
     * @return array|false
     */
    public static function getUserInfo($userId, $currentUserId = 0)
    {
        try {   

            $appent = ['photo_wall','call_prices','image_labels','level_info','gift_wall','user_stats','avatar_info','background_info'];

            // 只查询需要的基础字段
            $userInfo = User::where(['id' => $userId])
                ->field('id,career_name,nickname,is_online,is_busy,avatar,thumb_avatar,age,sex,birthday,height,weight,province,city,constellation,is_auth,emotion_status,level_id,video_price,voice_price')
                ->find()->append($appent)->toArray();

            // 8. 处理头像审核状态
            $avatarInfo = self::getAvatarInfo($userId, $currentUserId, $userInfo['avatar'],$userInfo['thumb_avatar']);

            // 9. 用户背景墙处理状态
            $backgroundInfo = self::getBackgroundInfo($userId, $currentUserId, $userInfo['photo_wall']['photos']);

            // 组装返回数据
            $result = [
                // 基本信息
                'user_id'           => $userInfo['id'],
                'nickname'          => $userInfo['nickname'],
                'is_online'         => $userInfo['is_online'],
                'is_busy'           => $userInfo['is_busy'],
                'avatar'            => $avatarInfo['avatar'],
                'thumb_avatar'      => $avatarInfo['thumb_avatar'],
                'is_avatar_reviewing' => $avatarInfo['is_reviewing'],
                'age'               => $userInfo['age'],
                'sex'               => $userInfo['sex'],
                'birthday'          => $userInfo['birthday'],
                'height'            => $userInfo['height'],
                'weight'            => $userInfo['weight'],
                'province'          => $userInfo['province'],
                'city'              => $userInfo['city'],
                'constellation'     => $userInfo['constellation'],
                'is_auth'           => $userInfo['is_auth'],

                // 照片墙
                'photo_wall'        => $backgroundInfo['background'],
                'is_photo_wall_reviewing' => $backgroundInfo['is_reviewing'],

                // 通话费用
                'video_price'       => $userInfo['call_prices']['video_price'],
                'voice_price'       => $userInfo['call_prices']['voice_price'],

                // 职业信息
                'career_name'       => $userInfo['career_name'],

                // 形象标签
                'image_labels'      => $userInfo['image_labels'],

                // 等级信息
                'level'             => $userInfo['level_info']['level_bg'],

                // 情感状态
                'emotion_status'    => $userInfo['emotion_status'],

                // 最近活跃时间
                //'last_active_time'  => $lastActiveTime,

                // 礼物墙
                'gift_wall'         => $userInfo['gift_wall'],

                // 用户统计数据
                'follow_count'      => $userInfo['user_stats']['follow_count'],      // 关注人数量
                'fans_count'        => $userInfo['user_stats']['fans_count'],        // 粉丝数量
                'gift_count'        => $userInfo['user_stats']['gift_count'],        // 收礼数量
                'visitor_count'     => $userInfo['user_stats']['visitor_count']      // 访客数量
            ];
            return $result;

        } catch (\Exception $e) {
            self::setError($e->getMessage());
            return false;
        }
    }

    /**
     * @notes 获取我的个人信息
     * @param int $userId 当前登录用户ID
     * @return array|false
     */
    public static function getMyInfo($userId)
    {
        try {
            $appent = ['photo_wall','call_prices','image_labels','level_info','user_stats','avatar_info','background_info'];

            // 只查询需要的基础字段
            $userInfo = User::where(['id' => $userId])
                ->field('id,career_name,nickname,is_online,is_busy,avatar,thumb_avatar,age,sex,birthday,height,weight,province,city,constellation,is_auth,emotion_status,level_id,video_price,voice_price')
                ->find()->append($appent)->toArray();

            if (!$userInfo) {
                throw new \Exception('用户不存在');
            }

            // 处理头像审核状态（查看自己时显示审核中的头像）
            $avatarInfo = self::getAvatarInfo($userId, $userId, $userInfo['avatar'],$userInfo['thumb_avatar']);

            // 用户背景墙处理状态（查看自己时显示审核中的背景）
            $backgroundInfo = self::getBackgroundInfo($userId, $userId, $userInfo['photo_wall']['photos']);

            // 组装返回数据
            $result = [
                // 基本信息
                'user_id'           => $userInfo['id'],
                'nickname'          => $userInfo['nickname'],
                'is_online'         => $userInfo['is_online'],
                'is_busy'           => $userInfo['is_busy'],
                'avatar'            => $avatarInfo['avatar'],
                'thumb_avatar'      => $avatarInfo['thumb_avatar'],
                'is_avatar_reviewing' => $avatarInfo['is_reviewing'],
                'age'               => $userInfo['age'],
                'sex'               => $userInfo['sex'],
                'birthday'          => $userInfo['birthday'],
                'height'            => $userInfo['height'],
                'weight'            => $userInfo['weight'],
                'province'          => $userInfo['province'],
                'city'              => $userInfo['city'],
                'constellation'     => $userInfo['constellation'],
                'is_auth'           => $userInfo['is_auth'],

                // 照片墙
                'photo_wall'        => $backgroundInfo['background'],
                'is_photo_wall_reviewing' => $backgroundInfo['is_reviewing'],

                // 通话费用
                'video_price'       => $userInfo['call_prices']['video_price'],
                'voice_price'       => $userInfo['call_prices']['voice_price'],

                // 职业信息
                'career_name'       => $userInfo['career_name'],

                // 形象标签
                'image_labels'      => $userInfo['image_labels'],

                // 等级信息
                'level'             => $userInfo['level_info']['level_bg'],

                // 情感状态
                'emotion_status'    => $userInfo['emotion_status'],

                // 用户统计数据
                'follow_count'      => $userInfo['user_stats']['follow_count'],      // 关注人数量
                'fans_count'        => $userInfo['user_stats']['fans_count'],        // 粉丝数量
                'gift_count'        => $userInfo['user_stats']['gift_count'],        // 收礼数量
                'visitor_count'     => $userInfo['user_stats']['visitor_count'],     // 访客数量
                'dynamic_count'     => $userInfo['user_stats']['dynamic_count'],     // 动态数量
                'comment_count'     => $userInfo['user_stats']['comment_count'],     // 评论数量
                'like_count'        => $userInfo['user_stats']['like_count'],        // 点赞数量
            ];
            return $result;

        } catch (\Exception $e) {
            self::setError($e->getMessage());
            return false;
        }
    }

    /**
     * @notes 获取他人信息
     * @param int $userId 要查看的用户ID
     * @param int $currentUserId 当前登录用户ID
     * @return array|false
     */
    public static function getOtherUserInfo($userId, $currentUserId = 0)
    {
        try {
            // 使用专门的获取器，包含他人背景墙获取器
            $appent = ['call_prices','image_labels','level_info','gift_wall','other_background'];

            // 只查询需要的基础字段
            $userInfo = User::where(['id' => $userId])
                ->field('id,career_name,nickname,is_online,is_busy,avatar,thumb_avatar,age,sex,birthday,height,weight,province,city,constellation,is_auth,emotion_status,level_id,video_price,voice_price')
                ->find()->append($appent)->toArray();

            if (!$userInfo) {
                throw new \Exception('用户不存在');
            }

            // 添加访客记录（如果当前用户已登录且不是查看自己）
            if ($currentUserId > 0 && $currentUserId != $userId) {
                UserVisitor::addVisitorRecord($currentUserId, $userId);
            }

            // 组装返回数据
            $result = [
                // 基本信息
                'user_id'           => $userInfo['id'],
                'nickname'          => $userInfo['nickname'],
                'is_online'         => $userInfo['is_online'],
                'is_busy'           => $userInfo['is_busy'],
                'avatar'            => $userInfo['avatar'] ? FileService::getFileUrl($userInfo['avatar']) : '',
                'thumb_avatar'      => $userInfo['thumb_avatar'] ? FileService::getFileUrl($userInfo['thumb_avatar']) : '',
                'is_avatar_reviewing' => 0, // 查看他人时始终为0
                'age'               => $userInfo['age'],
                'sex'               => $userInfo['sex'],
                'birthday'          => $userInfo['birthday'],
                'height'            => $userInfo['height'],
                'weight'            => $userInfo['weight'],
                'province'          => $userInfo['province'],
                'city'              => $userInfo['city'],
                'constellation'     => $userInfo['constellation'],
                'is_auth'           => $userInfo['is_auth'],

                // 照片墙（使用专门的他人背景墙获取器）
                'photo_wall'        => $userInfo['other_background']['background'],
                'is_photo_wall_reviewing' => $userInfo['other_background']['is_reviewing'], // 查看他人时始终为0

                // 通话费用
                'video_price'       => $userInfo['call_prices']['video_price'],
                'voice_price'       => $userInfo['call_prices']['voice_price'],

                // 职业信息
                'career_name'       => $userInfo['career_name'],

                // 形象标签
                'image_labels'      => $userInfo['image_labels'],

                // 等级信息
                'level'             => $userInfo['level_info']['level_bg'],

                // 情感状态
                'emotion_status'    => $userInfo['emotion_status'],

                // 礼物墙
                'gift_wall'         => $userInfo['gift_wall'],

                // 关注状态信息
                'follow_status'     => self::getFollowStatus($currentUserId, $userId),      // 我对他的关注状态：0=未关注，1=已关注
            ];
            return $result;

        } catch (\Exception $e) {
            self::setError($e->getMessage());
            return false;
        }
    }



    /**
     * @notes 获取最近活跃时间显示
     * @param array $userInfo
     * @return string
     */
    private static function getLastActiveTime($userInfo)
    {
        // 如果用户设置不显示活跃时间
        if ($userInfo['is_online_time'] == 0) {
            return '';
        }

        $lastOnlineTime = $userInfo['last_online_time'];
        if (!$lastOnlineTime) {
            return '';
        }

        $now = time();
        $diff = $now - $lastOnlineTime;

        if ($diff < 60) {
            return '刚刚活跃';
        } elseif ($diff < 3600) {
            return floor($diff / 60) . '分钟前活跃';
        } elseif ($diff < 86400) {
            return floor($diff / 3600) . '小时前活跃';
        } else {
            return floor($diff / 86400) . '天前活跃';
        }
    }

    /**
     * @notes 获取背景墙信息（包含审核状态）
     * @param int $userId 要查看的用户ID
     * @param int $currentUserId 当前登录用户ID
     * @param array $backgroundPhotos 用户当前背景墙图片列表
     * @return array
     */
    private static function getBackgroundInfo($userId, $currentUserId, $backgroundPhotos)
    {
        // 默认返回值
        $result = [
            'background' => $backgroundPhotos,
            'is_reviewing' => 0 // 0=不在审核中，1=审核中
        ];

        // 检查是否是当前登录用户查看自己的资料
        if ($userId == $currentUserId && $currentUserId > 0) {
            // 查询是否有待审核的背景墙
            $reviewingAvatar = Db::name('user_data_review')->where([
                'user_id' => $userId,
                'type' => 2, // 1=头像
                'status' => 0 // 0=待审核
            ])->order('id desc')->find();

            // 如果有待审核的背景墙
            if ($reviewingAvatar) {
                //image 字段是json格式 返回的数组还要拼接上域名
                $background = [];
                $background = json_decode($reviewingAvatar['image'], true);
                foreach ($background as $key => $value) {
                    $background[$key] = FileService::getFileUrl($value);
                }
                $result['background'] = $background;
                $result['is_reviewing'] = 1; // 标记为审核中
            }
        }

        return $result;
    }

    /**
     * @notes 获取头像信息（包含审核状态）
     * @param int $userId 要查看的用户ID
     * @param int $currentUserId 当前登录用户ID
     * @param string $userAvatar 用户当前头像
     * @return array
     */
    private static function getAvatarInfo($userId, $currentUserId, $userAvatar,$userThumbAvatar)

    {
        // 默认返回值
        $result = [
            'avatar'        => $userAvatar ? FileService::getFileUrl($userAvatar) : '',
            'thumb_avatar'  => $userThumbAvatar ? FileService::getFileUrl($userThumbAvatar) : '',
            'is_reviewing'  => 0 // 0=不在审核中，1=审核中
        ];

        // 检查是否是当前登录用户查看自己的资料
        if ($userId == $currentUserId && $currentUserId > 0) {
            // 查询是否有待审核的头像（
            $reviewingAvatar = UserDataReview::where([
                'user_id'   => $userId,
                'type'      => 1, // 1=头像
                'status'    => 0 // 0=待审核
            ])->order('id desc')->find();

            // 如果有待审核的头像
            if ($reviewingAvatar) {
                $result['avatar'] = $reviewingAvatar['image'] ? FileService::getFileUrl($reviewingAvatar['image']) : '';
                $result['thumb_avatar'] = $reviewingAvatar['thumb_avatar'] ? FileService::getFileUrl($reviewingAvatar['thumb_avatar']) : '';
                $result['is_reviewing'] = 1; // 标记为审核中
            }
        }

        return $result;
    }

    /**
     * @notes 更新用户形象标签关联
     * @param int $userId 用户ID
     * @param array $labelIds 标签ID数组
     * @return void
     * @throws \Exception
     */
    private static function updateUserImageLabels($userId, $labelIds = [])
    {
        // 删除用户现有的形象标签关联
        UserLabel::where('user_id', $userId)->delete();

        // 如果有新的标签ID，则插入新的关联
        if (!empty($labelIds)) {
            $insertData = [];
            foreach ($labelIds as $labelId) {
                $insertData[] = [
                    'user_id'       => $userId,
                    'image_label'   => $labelId,
                    'create_time'   => time(),
                ];
            }

            if (!empty($insertData)) {
                $result = UserLabel::insertAll($insertData);
                if (!$result) {
                    throw new \Exception('更新用户形象标签失败');
                }
            }
        }
    }


    /**
     * @notes 获取用户形象标签列表
     * @param int $userId 用户ID
     * @return array
     */
    public static function getImageLabelList($userId)
    {
        try {
            // 获取所有标签数据
            $allLabels = UserImageLabel::field('id, pid, name, inco, sort')
                    ->order('sort desc, id asc')
                    ->select()
                    ->toArray();

            // 获取用户已选择的标签ID
            $userSelectedLabels = UserLabel::where('user_id', $userId)
                    ->column('image_label');

            // 构建层级结构
            $result = [];
            $labelMap = [];

            // 先建立映射关系
            foreach ($allLabels as $label) {
                $labelMap[$label['id']] = $label;
            }

            // 构建分类结构
            foreach ($allLabels as $label) {
                if ($label['pid'] == 0) {
                    // 父级分类
                    $category = [
                        'id'    => $label['id'],
                        'name'  => $label['name'],
                        'inco'  => $label['inco'],
                        'sort'  => $label['sort'],
                        'children' => []
                    ];

                    // 查找子级标签
                    foreach ($allLabels as $childLabel) {
                        if ($childLabel['pid'] == $label['id']) {
                            $category['children'][] = [
                                'image_label_id' => $childLabel['id'],
                                'name' => $childLabel['name'],
                                'sort' => $childLabel['sort'],
                                'status' => in_array($childLabel['id'], $userSelectedLabels) ? 1 : 0  // 0未选中 1已选中
                            ];
                        }
                    }

                    // 对子级标签按sort排序
                    usort($category['children'], function($a, $b) {
                        return $b['sort'] - $a['sort'];
                    });

                    $result[] = $category;
                }
            }

            return $result;

        } catch (\Exception $e) {
            self::setError($e->getMessage());
            return false;
        }
    }

    /**
     * @notes 获取用户职业列表
     * @return array|false
     */
    public static function getCareerList()
    {
        try {
            $result = UserCareer::field('id as career_id,name')
                ->order(['sort' => 'desc', 'id' => 'asc'])
                ->select()
                ->toArray();

            return $result;

        } catch (\Exception $e) {
            self::setError($e->getMessage());
            return false;
        }
    }

    /**
     * @notes 修改个人资料
     * @param array $params
     * @param int $userId
     * @return bool
     */
    public static function updateProfile($params, $userId)
    {
        // 开启事务
        Db::startTrans();
        try {
            // 验证用户是否存在
            $user = User::where(['id' => $userId])->find();
            if (!$user) {
                throw new \Exception('用户不存在');
            }


            // 根据生日计算年龄和星座
            $age = calculateAge($params['birthday']);
            if($age < 18){
                throw new \Exception('您的年龄未满18岁');
            }
            $constellation = get_constellation($params['birthday']);

            // 更新用户信息
            $updateData = [
                'nickname'        => $params['nickname'],
                'emotion_status'  => $params['emotion_status'],
                'birthday'        => $params['birthday'],
                'age'             => $age,
                'height'          => $params['height'],
                'weight'          => $params['weight'],
                'province'        => $params['province'],
                'city'            => $params['city'],
                'constellation'   => $constellation,
                'career_name'     => $params['career_name'],
                'update_time'     => time(),
            ];

            // 处理形象标签关联
            self::updateUserImageLabels($userId, $params['image_label_ids'] ?? []);

            // 处理头像审核（传递当前用户数据用于比较）
            self::handleAvatarReview($userId, $params, $updateData, $user);

            // 处理背景图片审核（传递当前用户数据用于比较）
            self::handleBackgroundReview($userId, $params, $user);

            // 更新用户信息
            $result = User::where(['id' => $userId])->update($updateData);

            // 更新IM用户资料
            $imResult = update_im_user_info($userId);
            
            if (!$result) {
                throw new \Exception('更新用户信息失败');
            }

            // 提交事务
            Db::commit();
            return true;

        } catch (\Exception $e) {
            // 回滚事务
            Db::rollback();
            self::setError($e->getMessage());
            return false;
        }
    }

    /**
     * @notes 处理头像审核逻辑
     * @param int $userId 用户ID
     * @param array $params 请求参数
     * @param array &$updateData 更新数据引用
     * @param object $currentUser 当前用户数据
     * @return void
     */
    private static function handleAvatarReview($userId, $params, &$updateData, $currentUser)
    {
        // 检查头像是否发生改变
        $newAvatarUrl = FileService::setFileUrl($params['avatar']);
        $newThumbAvatarUrl = FileService::setFileUrl($params['thumb_avatar']);
        $currentAvatarUrl = FileService::setFileUrl($currentUser['avatar']) ?? '';
        
        // 如果头像没有发生改变，直接返回，不处理审核
        if ($newAvatarUrl === $currentAvatarUrl) {
            return;
        }

        // 判断头像是否需要审核
        $isAvatarExamine = ConfigService::get('systemconfig', 'is_avatar_examine', 0);

        if ($isAvatarExamine == 1) {
            if (!$params['is_avatar_reviewing']) {
                // 需要审核，创建审核记录，不更新用户头像
                $avatarUrl = $newAvatarUrl;
                $thumb_avatar = $newThumbAvatarUrl;

                // 查找头像是否有审核状态数据
                $UserDataReview = UserDataReview::where([
                    'user_id' => $userId,
                    'type' => 1,
                    'status' => 0
                ])->findOrEmpty();

                // 如果没有审核状态数据，或者审核数据与新头像不同，则创建/更新审核记录
                if ($UserDataReview->isEmpty()) {
                    // 创建新的审核记录
                    UserDataReview::create([
                        'user_id'       => $userId,
                        'image'         => $avatarUrl,
                        'thumb_avatar'  => $thumb_avatar,
                        'type'          => 1, // 1=头像
                        'status'        => 0, // 0=待审核
                        'create_time'   => time(),
                        'update_time'   => time()
                    ]);
                } else {
                    // 更新现有的审核记录
                    $UserDataReview->save([
                        'image'         => $avatarUrl,
                        'thumb_avatar'  => $thumb_avatar,
                        'update_time'   => time()
                    ]);
                }
            }
        } else {
            // 不需要审核，直接更新头像
            $updateData['avatar'] = $newAvatarUrl;
            $updateData['thumb_avatar'] = $newThumbAvatarUrl;
        }
    }

    /**
     * @notes 处理背景图片审核逻辑
     * @param int $userId 用户ID
     * @param array $params 请求参数
     * @param object $currentUser 当前用户数据
     * @return void
     */
    private static function handleBackgroundReview($userId, $params, $currentUser)
    {
        // 获取当前用户的背景图片
        $currentBackgrounds = UserPhotoWall::where(['user_id' => $userId, 'type' => 1])
            ->column('url');

        // 处理新的背景图片
        $newBackgrounds = [];
        foreach ($params['background'] as $bgUrl) {
            $newBackgrounds[] = $bgUrl ? FileService::setFileUrl($bgUrl) : '';
        }

        // 比较背景图片是否发生改变
        sort($currentBackgrounds);
        sort($newBackgrounds);

        // 如果背景图片没有发生改变，直接返回，不处理审核
        if ($currentBackgrounds === $newBackgrounds) {
            return;
        }

        // 判断背景图片是否需要审核
        $isBackgroundExamine = ConfigService::get('systemconfig', 'is_photo_wall_examine', 0);

        if ($isBackgroundExamine == 1) {
            if (!$params['is_photo_wall_reviewing']) {
                $background = [];
                foreach ($params['background'] as $bgUrl) {
                    $background[] = $bgUrl ? FileService::setFileUrl($bgUrl) : '';
                }
                // 需要审核，创建审核记录，不更新用户背景图片
                $backgroundUrl = json_encode($background);

                // 查找背景图片是否有审核状态数据
                $UserDataReview = UserDataReview::where([
                    'user_id' => $userId,
                    'type' => 2,
                    'status' => 0
                ])->findOrEmpty();

                // 如果没有审核状态数据，或者审核数据与新背景不同，则创建/更新审核记录
                if ($UserDataReview->isEmpty()) {
                    // 创建新的审核记录
                    UserDataReview::create([
                        'user_id'       => $userId,
                        'image'         => $backgroundUrl,
                        'type'          => 2, // 2=背景图片
                        'status'        => 0, // 0=待审核
                        'create_time'   => time(),
                        'update_time'   => time()
                    ]);
                } else {
                    // 更新现有的审核记录
                    $UserDataReview->save([
                        'image'         => $backgroundUrl,
                        'update_time'   => time()
                    ]);
                }
            }
        } else {
            // 不需要审核，直接更新背景图片
            if (!empty($params['background'])) {
                // 先删除原有的背景图片
                UserPhotoWall::where(['user_id' => $userId, 'type' => 1])->delete();

                // 添加新的背景图片
                $photoData = [];
                foreach ($params['background'] as $bgUrl) {
                    $photoData[] = [
                        'user_id'       => $userId,
                        'type'          => 1, // 1=图片
                        'url'           => $bgUrl ? FileService::setFileUrl($bgUrl) : '',
                        'create_time'   => time(),
                    ];
                }

                if (!empty($photoData)) {
                    UserPhotoWall::insertAll($photoData);
                }
            }
        }
    }

    /**
     * @notes 用户认证提交
     * @param array $params 认证参数
     * @param int $userId 用户ID
     * @return bool
     */
    public static function submitUserAuth($params, $userId)
    {
        try {
            // 处理认证照片
            $authPhotoUrl = FileService::setFileUrl($params['auth_photo']);

            // 创建认证审核记录
            UserDataReview::create([
                'user_id'        => $userId,
                'image'          => $authPhotoUrl, // 单张图片直接存储URL
                'type'           => 3, // 3=主播认证
                'status'         => 0, // 0=待审核
                'create_time'    => time(),
                'update_time'    => time()
            ]);

            return true;

        } catch (\Exception $e) {
            self::setError($e->getMessage());
            return false;
        }
    }

    /**
     * @notes 获取用户认证状态
     * @param int $userId 用户ID
     * @return array|false
     */
    public static function getUserAuthStatus($userId)
    {
        try {
            // 验证用户是否存在
            $user = User::where(['id' => $userId])->find();
            if (!$user) {
                throw new \Exception('用户不存在');
            }

            // 如果用户已经认证通过
            if ($user['is_auth'] == 1) {
                return [
                    'status' => 1, // 1=已认证
                    'status_desc' => '已认证',
                    'auth_photo' => '',
                    'reject_reason' => ''
                ];
            }

            // 查询最新的认证记录
            $authRecord = UserDataReview::where([
                'user_id' => $userId,
                'type' => 3 // 3=主播认证
            ])->order('id desc')->find();

            if (!$authRecord) {
                // 未提交认证
                return [
                    'status' => 0, // 0=未认证
                    'status_desc' => '未认证',
                    'auth_photo' => '',
                    'reject_reason' => ''
                ];
            }

            // 处理认证照片URL
            $authPhoto = '';
            if ($authRecord['image']) {
                $authPhoto = FileService::getFileUrl($authRecord['image']);
            }

            // 根据审核状态返回不同信息
            $statusMap = [
                0 => '审核中',
                1 => '审核通过',
                2 => '审核驳回'
            ];

            return [
                'status' => $authRecord['status'] == 0 ? 3 : $authRecord['status'], // 2=审核中
                'status_desc' => $statusMap[$authRecord['status']] ?? '未知状态',
                'auth_photo' => $authPhoto,
                'reason' => $authRecord['reason'] ?? ''
            ];

        } catch (\Exception $e) {
            self::setError($e->getMessage());
            return false;
        }
    }

    /**
     * @notes 关注/取消关注用户
     * @param int $userId 当前用户ID
     * @param int $toUserId 被关注用户ID
     * @return array|false
     */
    public static function toggleFollow(int $userId, int $toUserId)
    {
        try {
            // 开启事务
            Db::startTrans();

            // 查询当前用户对目标用户的关注状态
            $myFollowRecord = UserFollow::where([
                'user_id' => $userId,
                'to_user_id' => $toUserId
            ])->find();

            // 查询目标用户对当前用户的关注状态
            $targetFollowRecord = UserFollow::where([
                'user_id' => $toUserId,
                'to_user_id' => $userId
            ])->find();

            $isFollow = false;
            $message = '';

            if ($myFollowRecord && ($myFollowRecord['status'] == 1 || $myFollowRecord['status'] == 3)) {
                // 当前用户已关注目标用户，执行取关操作

                if ($myFollowRecord['status'] == 3) {
                    // 当前是互关状态，取关后变为单向关注
                    $myFollowRecord->status = 0;
                    $myFollowRecord->save();

                    // 将对方的状态从互关改为关注
                    if ($targetFollowRecord && $targetFollowRecord['status'] == 3) {
                        $targetFollowRecord->status = 1;
                        $targetFollowRecord->save();
                    }
                } else {
                    // 当前是单向关注状态，取关
                    $myFollowRecord->status = 0;
                    $myFollowRecord->save();
                }

                $message = '取消关注成功';
                $isFollow = false;

            } else {
                // 当前用户未关注目标用户，执行关注操作

                if ($myFollowRecord) {
                    // 已存在记录但状态为取关，更新为关注
                    $myFollowRecord->status = 1;
                    $myFollowRecord->save();
                } else {
                    // 不存在记录，创建新的关注记录
                    $myFollowRecord = UserFollow::create([
                        'user_id' => $userId,
                        'to_user_id' => $toUserId,
                        'status' => 1,
                        'create_time' => time()
                    ]);
                }

                // 检查是否形成互关
                if ($targetFollowRecord && $targetFollowRecord['status'] == 1) {
                    // 对方也关注了我，形成互关
                    $myFollowRecord->status = 3;
                    $myFollowRecord->save();

                    $targetFollowRecord->status = 3;
                    $targetFollowRecord->save();
                }

                $message = '关注成功';
                $isFollow = true;
            }

            // 提交事务
            Db::commit();

            return [
                'is_follow' => $isFollow,
                'message' => $message
            ];

        } catch (\Exception $e) {
            // 回滚事务
            Db::rollback();
            self::setError($e->getMessage());
            return false;
        }
    }


    /**
     * @notes 获取用户之间的关注状态
     * @param int $currentUserId 当前用户ID
     * @param int $targetUserId 目标用户ID
     * @return array
     */
    private static function getFollowStatus($currentUserId, $targetUserId)
    {
        // 默认状态
        $follow_status = 0;

        // 如果当前用户未登录，返回默认状态
        if (!$currentUserId || $currentUserId == $targetUserId) {
            return $follow_status;
        }

        // 查询我对目标用户的关注状态
        $myFollowRecord = UserFollow::where([
            'user_id' => $currentUserId,
            'to_user_id' => $targetUserId
        ])->find();
        

        // 设置我对他的关注状态
        if ($myFollowRecord && ($myFollowRecord['status'] == 1 || $myFollowRecord['status'] == 3)) {
            $follow_status = 1;
        }

        return $follow_status;
    }

    /**
     * @notes 获取当前用户余额和收益信息
     * @param int $userId 用户ID
     * @return array|false
     */
    public static function getBalanceInfo($userId)
    {
        // 获取用户余额信息
        $userBalance = UserBalance::getUserBalance($userId);

        if (!$userBalance) {
            // 如果没有余额记录，返回默认值
            return [
                'balance' => '0'.ConfigService::get('systemconfig','currency_name'),      // 余额（金币）
                'income' => '0'.ConfigService::get('systemconfig','profit_name')   // 收益
            ];
        }

        return [
            'balance' => (int)$userBalance['balance'].ConfigService::get('systemconfig','currency_name'),           // 余额（金币）
            'income' => number_format($userBalance['income'], 2).ConfigService::get('systemconfig','profit_name') // 收益，格式化为两位小数
        ];
    }

}
