# 实时扣费功能总结

## 功能概述

实时扣费API已成功实现，支持视频通话过程中的按分钟自动扣费，具备完善的余额检查和预警机制。

## 核心特性

### 1. 智能扣费机制
- **按分钟计费**: 每满60秒扣除一分钟费用
- **避免重复扣费**: 系统记录已扣费分钟数，防止重复扣费
- **实时轮询**: 前端每5秒调用一次，及时响应扣费需求

### 2. 双重余额保护
- **当前扣费检查**: 确保余额足够支付当前分钟费用
- **下分钟预检查**: 同时检查是否能支付下一分钟费用
- **提前结束通话**: 余额不足时自动结束通话，避免欠费

### 3. 用户体验优化
- **余额预警**: 余额不足两分钟费用时发出警告
- **倒计时显示**: 显示距离下次扣费的剩余时间
- **状态反馈**: 实时反馈扣费状态和余额变化

## 技术实现

### 文件结构
```
app/applent/controller/video/VideoController.php  # API控制器
app/applent/logic/video/VideoCallLogic.php        # 业务逻辑
app/applent/validate/video/VideoCallValidate.php  # 参数验证
Docs/实时扣费API文档.md                           # API文档
Docs/实时扣费API测试示例.md                       # 测试示例
```

### 核心方法
- `VideoController::real_time_charge()` - API入口
- `VideoCallLogic::realTimeCharge()` - 扣费业务逻辑
- `VideoCallLogic::endCallForInsufficientBalance()` - 余额不足处理

### 数据库操作
- **la_video_call_record**: 更新通话记录的总消费金额
- **video_charging_record**: 记录每次扣费详情
- **la_user_balance**: 扣除用户余额

## 业务流程

### 扣费检查流程
1. **验证通话状态** → 只有接通状态才能扣费
2. **权限检查** → 只有付费方可以调用
3. **时长计算** → 计算通话时长和应扣费分钟数
4. **重复检查** → 避免同一分钟重复扣费
5. **余额验证** → 检查余额是否足够当前+下分钟费用
6. **执行扣费** → 扣除余额并更新记录
7. **状态反馈** → 返回扣费结果和余额信息

### 余额不足处理
1. **自动结束通话** → 更新通话状态为已结束
2. **恢复用户状态** → 将双方is_busy设为0
3. **记录挂断信息** → 标记付费方主动挂断

## API响应数据

### 扣费成功响应
```json
{
    "need_charge": true,
    "message": "扣费成功",
    "charged_amount": 10,
    "charged_minutes": 1,
    "remaining_balance": 90,
    "next_charge_in": 55,
    "next_minute_cost": 10,
    "can_afford_next": true,
    "balance_warning": false
}
```

### 余额警告响应
```json
{
    "need_charge": false,
    "message": "当前分钟已扣费，等待下一分钟",
    "current_balance": 15,
    "next_minute_cost": 10,
    "can_afford_next": true,
    "balance_warning": true
}
```

## 安全机制

### 1. 权限控制
- 只有通话发起人（付费方）可以调用扣费接口
- 验证通话记录的真实性和有效性

### 2. 数据一致性
- 使用数据库事务确保扣费操作的原子性
- 异常情况下自动回滚，保证数据一致性

### 3. 防重复扣费
- 基于已扣费分钟数判断是否需要扣费
- 避免网络延迟导致的重复扣费

### 4. 余额保护
- 双重余额检查机制
- 余额不足时主动结束通话

## 前端集成要点

### 1. 轮询机制
```javascript
// 每5秒轮询一次
setInterval(callChargeAPI, 5000);
```

### 2. 错误处理
```javascript
if (result.code === 0) {
    // 扣费失败，停止轮询并结束通话
    clearInterval(pollingInterval);
    endCall();
}
```

### 3. 用户提醒
```javascript
if (data.balance_warning) {
    showWarning('余额不足，请及时充值');
}
```

## 性能优化

### 1. 数据库优化
- 使用索引优化查询性能
- 批量更新减少数据库操作

### 2. 缓存机制
- 用户余额信息缓存
- 通话状态缓存

### 3. 异常处理
- 完善的异常捕获和处理
- 详细的错误日志记录

## 测试建议

### 1. 功能测试
- 正常扣费流程测试
- 余额不足场景测试
- 重复调用防护测试

### 2. 性能测试
- 并发请求处理能力
- 响应时间测试
- 内存使用情况

### 3. 边界测试
- 网络异常情况
- 数据库连接异常
- 极端余额情况

## 监控指标

### 1. 业务指标
- 扣费成功率
- 通话异常结束率
- 用户余额不足率

### 2. 技术指标
- API响应时间
- 数据库查询性能
- 错误率统计

## 后续优化方向

### 1. 功能增强
- 支持优惠券抵扣
- 分级定价策略
- 余额自动充值

### 2. 用户体验
- 更精准的余额预警
- 通话质量与计费关联
- 历史账单查询

### 3. 系统优化
- 异步处理机制
- 分布式锁防并发
- 实时推送通知

## 总结

实时扣费API已完整实现，具备：
- ✅ 按分钟精确计费
- ✅ 双重余额保护机制
- ✅ 完善的异常处理
- ✅ 用户友好的状态反馈
- ✅ 高性能的轮询机制

该功能可以有效保障通话计费的准确性和用户体验的流畅性。
