# 基于扣费时间的测试场景

## 测试逻辑说明

新的扣费逻辑基于**上次扣费时间**而不是通话总时长，具体规则：

1. **首次扣费**：通话接通满60秒后扣费
2. **后续扣费**：距离上次扣费满60秒后扣费
3. **固定金额**：每次扣费固定为1分钟费用
4. **时间记录**：通过`video_charging_record.end_time`字段记录上次扣费时间

## 测试场景

### 场景1：首次扣费前（通话45秒）

**数据准备**：
- 通话记录：turn_time = 当前时间-45秒，status = 1
- 扣费记录：无记录

**API调用**：
```json
POST /video/real_time_charge
{
    "call_id": 123
}
```

**预期响应**：
```json
{
    "code": 1,
    "data": {
        "need_charge": false,
        "message": "通话时长不足1分钟，无需扣费",
        "call_duration": 45,
        "next_charge_in": 15
    }
}
```

### 场景2：首次扣费（通话65秒）

**数据准备**：
- 通话记录：turn_time = 当前时间-65秒，status = 1
- 扣费记录：无记录
- 用户余额：100金币

**API调用**：
```json
POST /video/real_time_charge
{
    "call_id": 123
}
```

**预期响应**：
```json
{
    "code": 1,
    "data": {
        "need_charge": true,
        "message": "扣费成功",
        "charged_amount": 10,
        "charged_minutes": 1,
        "remaining_balance": 90,
        "last_charge_time": 1672531260,
        "next_charge_in": 60
    }
}
```

**数据库变化**：
- `video_charging_record`表新增记录，`end_time`为当前时间
- `la_user_balance`表余额减少10金币
- `la_video_call_record`表`total_coin`增加10金币

### 场景3：第二次扣费前（距离上次扣费45秒）

**数据准备**：
- 通话记录：turn_time = 当前时间-125秒，status = 1
- 扣费记录：end_time = 当前时间-45秒，sum = 1
- 用户余额：90金币

**API调用**：
```json
POST /video/real_time_charge
{
    "call_id": 123
}
```

**预期响应**：
```json
{
    "code": 1,
    "data": {
        "need_charge": false,
        "message": "距离上次扣费不足1分钟，无需扣费",
        "call_duration": 125,
        "time_since_last_charge": 45,
        "next_charge_in": 15,
        "current_balance": 90
    }
}
```

### 场景4：第二次扣费（距离上次扣费65秒）

**数据准备**：
- 通话记录：turn_time = 当前时间-185秒，status = 1
- 扣费记录：end_time = 当前时间-65秒，sum = 1
- 用户余额：90金币

**API调用**：
```json
POST /video/real_time_charge
{
    "call_id": 123
}
```

**预期响应**：
```json
{
    "code": 1,
    "data": {
        "need_charge": true,
        "message": "扣费成功",
        "charged_amount": 10,
        "charged_minutes": 1,
        "remaining_balance": 80,
        "last_charge_time": 1672531320,
        "next_charge_in": 60
    }
}
```

**数据库变化**：
- `video_charging_record`表更新：`coin`累加10，`sum`累加1，`end_time`更新为当前时间
- `la_user_balance`表余额减少10金币
- `la_video_call_record`表`total_coin`累加10金币

### 场景5：余额不足（余额15金币，单价10金币）

**数据准备**：
- 通话记录：turn_time = 当前时间-245秒，status = 1
- 扣费记录：end_time = 当前时间-65秒，sum = 2
- 用户余额：15金币（不足支付当前10金币+下次10金币=20金币）

**API调用**：
```json
POST /video/real_time_charge
{
    "call_id": 123
}
```

**预期响应**：
```json
{
    "code": 0,
    "msg": "余额不足以支付后续通话费用，通话已结束"
}
```

**数据库变化**：
- `la_video_call_record`表：`status`更新为2（已结束），`end_time`更新为当前时间
- `la_user`表：双方用户`is_busy`更新为0

## 时间轴示例

假设通话在 `10:00:00` 接通，单价10金币/分钟：

```
10:00:00 - 通话接通 (turn_time)
10:00:45 - 轮询API：通话45秒，无需扣费，还需15秒
10:01:05 - 轮询API：通话65秒，首次扣费10金币，记录扣费时间10:01:05
10:01:50 - 轮询API：距离上次扣费45秒，无需扣费，还需15秒
10:02:10 - 轮询API：距离上次扣费65秒，第二次扣费10金币，记录扣费时间10:02:10
10:03:15 - 轮询API：距离上次扣费65秒，第三次扣费10金币，记录扣费时间10:03:15
...
```

## 关键优势

### 1. 精确计费
- 不依赖通话总时长，避免网络延迟导致的计费误差
- 每次扣费固定1分钟，计费逻辑简单明确

### 2. 防重复扣费
- 基于上次扣费时间判断，天然防止重复扣费
- 即使前端多次调用，也不会在同一分钟内重复扣费

### 3. 用户友好
- 余额不足时提前结束通话，避免欠费
- 提供详细的时间信息，用户可预知下次扣费时间

### 4. 系统稳定
- 扣费逻辑独立于网络状况
- 数据库事务保证数据一致性

## 测试验证要点

1. **时间精度**：确保扣费时间判断准确到秒
2. **并发安全**：多个请求同时到达时的处理
3. **异常处理**：网络中断、数据库异常等情况
4. **边界条件**：恰好60秒时的扣费行为
5. **余额检查**：各种余额情况下的处理逻辑
