<?php

namespace app\applent\logic\index;

use app\common\logic\BaseLogic;
use app\common\model\banner\Banner;
use app\common\model\label\UserLabel;
use app\common\model\bulletin\Bulletin;
use think\facade\Cache;
use app\common\service\ConfigService;
use app\common\model\user\User;
use app\common\model\custom\CustomAutoMsg;
use app\applent\logic\user\LoginLogic;
class IndexLogic extends BaseLogic
{
    /**
     * 获取配置信息
     * @return array
     */
    public static function getConfig($params)
    {
        try {
            // 验证签名
            $decryptedSign = decrypt($params['sign']);
            if (!is_numeric($decryptedSign) || $decryptedSign > time()) {
                throw new \Exception('非法请求');
            }

            if (time() - $decryptedSign > 300) {
                throw new \Exception('请求超时');
            }
            //查找系统配置
            $config = ConfigService::get('systemconfig');
            //用户协议
            $service_content = ConfigService::get('agreement', 'service_content');
            //隐私协议
            $privacy_content = ConfigService::get('agreement', 'privacy_content');
            $config['service_content'] = $service_content;
            $config['privacy_content'] = $privacy_content;
            $config['first_charge_reward'] = $config['first_charge_reward'].$config['currency_name'];

            $tencent_im_config = ConfigService::get('tencent_im_config');
            $config['tencent_group_id'] = $tencent_im_config['tencent_group_id'];
            $config['tencent_sdkappid'] = $tencent_im_config['tencent_sdkappid'];
            $config['tencent_api_secret_key'] = $tencent_im_config['tencent_api_secret_key'];
            $config['tencent_identifier'] = $tencent_im_config['tencent_identifier'];
            return $config;

        } catch (\Exception $e) {
            self::setError($e->getMessage());
            return false;
        }

        
        return $config;
    }

    /**
     * 获取轮播图列表
     * @param array $params
     * @return array
     */
    public static function bannerList($params)
    {
        return Banner::where('status', 1)
        ->where('position', $params['position'])
        ->field('id, title, desc, image, link')
        ->order('sort desc, id desc')
        ->select()
        ->toArray();
    }

    /**
     * 获取用户标签列表
     * @param array $params
     * @return array
     */
    public static function labelList()
    {
        return UserLabel::where('status', 1)
        ->field('id, name')
        ->order('is_default desc,sort desc, id desc')
        ->select()
        ->toArray();
    }

    /**
     * 获取公告列表
     * @param array $params
     * @return array
     */
    public static function bulletinList()
    {
        $bulletin = Bulletin::where('is_delete', 0)
        ->field('id, title, content,create_time')
        ->order('create_time desc')
        ->find();
        return $bulletin ? [$bulletin] : [];
    }

    /**
     * 用户置顶
     * @param array $user_id
     * @return array
     */
    public static function userTop($user_id)
    {
        try {
            //用户点击置顶时间不能超出config设置的置顶间隔
            $top_time = ConfigService::get('systemconfig', 'top_time', 60);

            $user = User::where('id', $user_id)
                ->field('id,top_time,is_recommend,is_gaoyan,sex,create_time')
                ->find();
            if (!$user) {
                throw new \Exception('用户不存在');
            }

            // 检查时间间隔
            if ($user->top_time && (time() - $user->top_time < $top_time)) {
                $remain = $top_time - (time() - $user->top_time);
                throw new \Exception("操作过于频繁，请{$remain}秒后再试");
            }

            $user->top_time  = time();
            $user->save();

            // 从配置获取新手天数（默认3天）
            $newUserDays = ConfigService::get('systemconfig', 'new_user_days', 3);
      
            // 判断是否为新人用户（注册时间在配置天数内）
            $isNewcomer = 0;
            if ($user->create_time) {
                $daysSinceRegistration = (time() - strtotime($user->create_time)) / (24 * 60 * 60);
                $isNewcomer = $daysSinceRegistration <= $newUserDays ? 1 : 0;
            }
            // 返回用户分类标识
            return [
                'is_recommend' => $user->is_recommend == 1 ? 1 : 0,         // 是否为推荐用户
                'is_gaoyan' => $user->is_gaoyan == 1 ? 1 : 0,               // 是否为高颜用户
                'is_nvshen' => $user->sex == 2 ? 1 : 0,                     // 是否为女神用户
                'is_new_user' => $isNewcomer,                               // 是否为新人用户
            ];
        } catch (\Exception $e) {
            throw new \Exception($e->getMessage());
        }

    }

    /**
     * 一键打招呼
     * @param array $params
     * @return array
     */
    public static function greetAction($userInfo)
    {
        $cacheKey = 'greet_cooldown:' . $userInfo['user_id'];
        if (Cache::has($cacheKey)) {
            $remain = Cache::get($cacheKey) - time();
            throw new \Exception("操作过于频繁，请{$remain}秒后再试");
        }
        
        if ($userInfo['sex'] != 2) {
            throw new \Exception('请求异常');
        }

        if($userInfo['is_auth'] != 1){
            throw new \Exception('请先认证');
        }

        $is_greet = ConfigService::get('systemconfig', 'is_greet');
        if (!$is_greet) {
            throw new \Exception('一键打招呼功能未开启');
        }

        //获取目标用户
        $user_where = [
            'is_online' => 1,
            'sex' => 1,
        ];
        $targetUsers = User::where($user_where)
            ->where('id', '<>', $userInfo['user_id'])
            ->orderRaw('RAND()') // 随机排序
            ->limit(10)
            ->column('id');

        if (empty($targetUsers)) {
            throw new \Exception('没有找到目标用户');
        }

        //获取随机招呼语
        $greet_where = [
            'type'     => 2,
            'user_id'  => 0,
            'classify' => 1,
            'status'   =>1
        ];
        $greetings = CustomAutoMsg::where($greet_where)
            ->orderRaw('RAND()') // 随机排序
            ->limit(10)
            ->column('msg');
        if (empty($greetings)) {
            $greetings = [
                '你好',
                'Hello',
            ];
        }
        //发送消息
        try {
            foreach ($targetUsers as $receiverId) {
                $message = $greetings[array_rand($greetings)];
                $result = send_im_text_msg($userInfo['user_id'],$receiverId,$message);
                // if(!$result['status']){
                //     throw new \Exception($result['data']['ErrorInfo']);
                // }
            }
            // 设置冷却时间
            $greet_time = ConfigService::get('systemconfig', 'greet_time');
            Cache::set($cacheKey, time() + $greet_time, $greet_time);
            return true;
        } catch (\Exception $e) {
            throw new \Exception($e->getMessage());
        }
    }

}
